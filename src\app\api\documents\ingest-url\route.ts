import { NextRequest, NextResponse } from 'next/server';
import urlDocumentService from '@/lib/url-document-service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { url, username, password, customHeaders } = body;

    // Validate required fields
    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Validate URL format
    const validation = await urlDocumentService.validateUrl(url);
    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    console.log(`Processing URL ingestion request for: ${url}`);

    // Ingest document from URL
    const result = await urlDocumentService.ingestFromUrl({
      url,
      username,
      password,
      customHeaders
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Document successfully ingested from URL',
      documentId: result.documentId,
      filename: result.filename,
      contentPreview: result.contentPreview
    });

  } catch (error) {
    console.error('URL ingestion API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'URL Document Ingestion API',
    usage: 'POST with { url, username?, password?, customHeaders? }',
    supportedProtocols: ['http', 'https'],
    supportedContentTypes: ['text/html']
  });
}
