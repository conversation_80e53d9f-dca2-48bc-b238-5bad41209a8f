// URL document ingestion service
import * as cheerio from 'cheerio';
import TurndownService from 'turndown';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import enhancedDocumentService from './enhanced-document-service';
import documentManager from './document-manager';

interface UrlIngestionOptions {
  url: string;
  username?: string;
  password?: string;
  customHeaders?: Record<string, string>;
}

interface UrlIngestionResult {
  success: boolean;
  documentId?: string;
  filename?: string;
  error?: string;
  contentPreview?: string;
}

class UrlDocumentService {
  private turndownService: TurndownService;
  private readonly uploadsDir = path.join(process.cwd(), 'uploads');

  constructor() {
    // Initialize Turndown service for HTML to Markdown conversion
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full',
    });

    // Configure Turndown rules
    this.turndownService.addRule('removeScripts', {
      filter: ['script', 'style', 'noscript'],
      replacement: () => ''
    });

    this.turndownService.addRule('cleanWhitespace', {
      filter: (node) => {
        return node.nodeName === '#text' && /^\s*$/.test(node.textContent || '');
      },
      replacement: () => ''
    });
  }

  async ingestFromUrl(options: UrlIngestionOptions): Promise<UrlIngestionResult> {
    try {
      // Validate URL
      const url = new URL(options.url);
      if (!['http:', 'https:'].includes(url.protocol)) {
        return {
          success: false,
          error: 'Only HTTP and HTTPS URLs are supported'
        };
      }

      // Prepare fetch options
      const fetchOptions: RequestInit = {
        method: 'GET',
        headers: {
          'User-Agent': 'ChatDoc URL Ingestion Service/1.0',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          ...options.customHeaders
        },
        timeout: 30000, // 30 second timeout
      };

      // Add authentication if provided
      if (options.username && options.password) {
        const auth = Buffer.from(`${options.username}:${options.password}`).toString('base64');
        fetchOptions.headers = {
          ...fetchOptions.headers,
          'Authorization': `Basic ${auth}`
        };
      }

      console.log(`Fetching content from URL: ${options.url}`);
      
      // Fetch the webpage
      const response = await fetch(options.url, fetchOptions);
      
      if (!response.ok) {
        return {
          success: false,
          error: `Failed to fetch URL: ${response.status} ${response.statusText}`
        };
      }

      const contentType = response.headers.get('content-type') || '';
      if (!contentType.includes('text/html')) {
        return {
          success: false,
          error: `Unsupported content type: ${contentType}. Only HTML content is supported.`
        };
      }

      // Get HTML content
      const html = await response.text();
      
      if (!html || html.trim().length === 0) {
        return {
          success: false,
          error: 'No content found at the provided URL'
        };
      }

      // Parse HTML and extract meaningful content
      const $ = cheerio.load(html);
      
      // Remove unwanted elements
      $('script, style, noscript, iframe, object, embed, form, input, button, nav, footer, aside, .advertisement, .ads, .sidebar').remove();
      
      // Try to find main content area
      let contentElement = $('main, article, .content, .post, .entry, #content, #main').first();
      if (contentElement.length === 0) {
        contentElement = $('body');
      }

      // Extract title
      let title = $('title').text().trim();
      if (!title) {
        title = $('h1').first().text().trim();
      }
      if (!title) {
        title = url.hostname;
      }

      // Clean up title for filename
      const cleanTitle = title
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .substring(0, 100);

      // Convert HTML to Markdown
      const htmlContent = contentElement.html() || '';
      let markdownContent = this.turndownService.turndown(htmlContent);
      
      // Add metadata header
      const metadata = `---
title: ${title}
source_url: ${options.url}
ingested_at: ${new Date().toISOString()}
content_type: webpage
---

# ${title}

Source: [${options.url}](${options.url})

---

${markdownContent}`;

      // Ensure uploads directory exists
      await fs.mkdir(this.uploadsDir, { recursive: true });

      // Generate unique filename
      const documentId = uuidv4();
      const filename = `${cleanTitle}-${documentId}.md`;
      const filePath = path.join(this.uploadsDir, filename);

      // Save markdown content to file
      await fs.writeFile(filePath, metadata, 'utf-8');

      // Calculate file size
      const stats = await fs.stat(filePath);
      const fileSize = stats.size;

      // Add to document manager
      await documentManager.addDocument({
        id: documentId,
        filename: filename,
        originalName: `${title} (from ${url.hostname})`,
        size: fileSize,
        type: '.md',
      });

      // Process with enhanced document service
      try {
        await enhancedDocumentService.addDocument(filePath, documentId, filename, fileSize);
        console.log(`Successfully processed URL document: ${filename}`);
      } catch (processingError) {
        console.error('Failed to process URL document:', processingError);
        // Continue even if processing fails - file is still saved
      }

      return {
        success: true,
        documentId,
        filename,
        contentPreview: markdownContent.substring(0, 500) + (markdownContent.length > 500 ? '...' : '')
      };

    } catch (error) {
      console.error('URL ingestion error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async validateUrl(url: string): Promise<{ valid: boolean; error?: string }> {
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          valid: false,
          error: 'Only HTTP and HTTPS URLs are supported'
        };
      }
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: 'Invalid URL format'
      };
    }
  }
}

// Singleton instance
const urlDocumentService = new UrlDocumentService();

export default urlDocumentService;
export type { UrlIngestionOptions, UrlIngestionResult };
